<template>
  <div class="agent-chat-message" :class="{ 'is-user': messageData.role === 'user' }">
    <div class="message-container">
      <div
        class="message-content"
        :class="{
          'loading-content': !messageData.isFinish && messageData.role === 'assistant',
        }"
      >
        <template
          v-if="
            messageData.isFinish ||
            messageData.content ||
            messageData.reasoningData.content ||
            messageData.preResponseContent
          "
        >
          <template v-if="messageData.role === 'assistant'">
            <AgentThinking
              v-if="messageData.reasoningData.content"
              :reasoning-data="messageData.reasoningData"
            />
            <!-- 预响应内容显示 -->
            <div
              v-if="messageData.preResponseContent && !messageData.content"
              class="pre-response-content"
            >
              <AgentMessageRender :text="messageData.preResponseContent" />
            </div>
            <!-- 正式回答内容显示 -->
            <AgentMessageRender v-if="messageData.content" :text="messageData.content" />
          </template>
          <span v-else class="user-message-content">{{ messageData.content }}</span>
        </template>
        <div v-else-if="!messageData.isFinish && messageData.role === 'assistant'" class="loading">
          <span class="loading-dots">
            <span class="dot dot1">.</span>
            <span class="dot dot2">.</span>
            <span class="dot dot3">.</span>
          </span>
        </div>
        <!-- 操作按钮 (仅在 role=assistant 时显示) -->
        <div
          v-if="messageData.isFinish && messageData.role === 'assistant' && messageData.content"
          class="action-buttons"
        >
          <div class="action-btn" @click="handleCopy">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { showSuccessToast } from 'vant';
import AgentMessageRender from './AgentMessageRender.vue';
import AgentThinking from './AgentThinking.vue';

interface IProps {
  messageData: IChatStreamContent;
}

const props = defineProps<IProps>();

// 复制消息内容
const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(props.messageData.content);
    showSuccessToast('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = props.messageData.content;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showSuccessToast('已复制到剪贴板');
  }
};
</script>

<style lang="scss" scoped>
.agent-chat-message {
  margin: 24px 0;
  display: flex;
  justify-content: flex-start;
  width: 100%;

  &.is-user {
    justify-content: flex-end;

    .message-container {
      .message-content {
        background: linear-gradient(135deg, #8B7ED8 0%, #B794F6 100%);
        color: white;
        border-radius: 16px 16px 4px 16px;
        border: 2px solid rgba(139, 126, 216, 0.3);

        .user-message-content {
          font-size: 20px;
          font-weight: 500;
          line-height: 1.5;
        }
      }
    }
  }

  .message-container {
    max-width: 85%;
    display: flex;
    flex-direction: column;

    .message-content {
      width: fit-content;
      max-width: 100%;
      padding: 20px;
      background: var(--bg-glass-light, rgba(255, 255, 255, 0.9));
      border: 2px solid var(--border-accent, rgba(139, 126, 216, 0.3));
      border-radius: 16px 16px 16px 4px;
      backdrop-filter: blur(20px);

      color: var(--text-primary, #333);
      font-size: 20px;
      font-weight: 400;
      line-height: 1.5;

      &.loading-content {
        border-radius: 16px 16px 16px 0px;
      }

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;

        .loading-dots {
          display: flex;
          gap: 4px;

          .dot {
            display: inline-block;
            opacity: 0.3;
            animation: dotPulse 0.8s infinite;
            color: var(--primary-color, #8B7ED8);
            font-size: 24px;
            font-weight: bold;
          }

          .dot1 {
            animation-delay: 0s;
          }

          .dot2 {
            animation-delay: 0.25s;
          }

          .dot3 {
            animation-delay: 0.5s;
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        padding-top: 12px;
        border-top: 1px solid var(--border-light, rgba(0, 0, 0, 0.1));

        .action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 8px;
          background: var(--bg-glass-light, rgba(255, 255, 255, 0.5));
          border: 1px solid var(--border-light, rgba(0, 0, 0, 0.1));
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--text-secondary, #666);

          &:hover {
            background: var(--bg-glass-medium, rgba(139, 126, 216, 0.1));
            border-color: var(--border-accent, rgba(139, 126, 216, 0.3));
            color: var(--primary-color, #8B7ED8);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          svg {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
}

// 动画
@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .agent-chat-message {
    margin: 16px 0;

    .message-container {
      max-width: 95%;

      .message-content {
        padding: 16px;
        border-radius: 12px 12px 12px 4px;
        font-size: 16px;

        &.loading-content {
          border-radius: 12px 12px 12px 0px;
        }

        .user-message-content {
          font-size: 16px;
        }

        .action-buttons {
          gap: 8px;
          margin-top: 12px;
          padding-top: 8px;

          .action-btn {
            width: 32px;
            height: 32px;

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }

    &.is-user {
      .message-container {
        .message-content {
          border-radius: 12px 12px 4px 12px;
        }
      }
    }
  }
}
</style>

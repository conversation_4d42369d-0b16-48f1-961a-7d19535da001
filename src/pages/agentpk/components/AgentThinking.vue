<template>
  <div class="agent-thinking-wrapper">
    <div class="agent-thinking-box" @click="handleToggleThinking">
      <div class="thinking-status">
        {{ reasoningData.status === ReasoningStatus.PROCESSING ? '思考中...' : '已深度思考' }}
      </div>
      <div class="thinking-toggle-icon">
        <svg v-if="expanded" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    <div v-show="expanded" class="agent-thinking-content">
      <!-- 渲染分离后的思考步骤 -->
      <div
        v-for="(step, index) in parsedThinkingSteps"
        :key="index"
        class="thinking-step"
        style="white-space: pre-wrap; word-break: break-word"
      >
        {{ step }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs } from 'vue';
import { ReasoningStatus } from '@/constants/chat';

interface IProps {
  reasoningData: IReasoningData;
}

const props = defineProps<IProps>();
const { reasoningData } = toRefs(props);
const expanded = ref(true);

// 解析思考步骤，将多个思考内容分离
const parsedThinkingSteps = computed(() => {
  const { content } = reasoningData.value;
  if (!content) return [];

  console.log('AgentThinking - 原始思考内容:', content);

  // 移除开头的换行符
  const cleanContent = content.startsWith('\n') ? content.replace(/^\n/, '') : content;

  // 去重处理：如果内容中有重复的[Reflection]块，只保留唯一的
  const steps: string[] = [];
  const seenSteps = new Set<string>();

  // 按照换行符分割内容
  const lines = cleanContent.split('\n');
  let currentStep = '';

  lines.forEach((line) => {
    if (line.trim() === '') {
      if (currentStep.trim()) {
        const trimmedStep = currentStep.trim();
        if (!seenSteps.has(trimmedStep)) {
          steps.push(trimmedStep);
          seenSteps.add(trimmedStep);
        }
        currentStep = '';
      }
    } else {
      currentStep += (currentStep ? '\n' : '') + line;
    }
  });

  // 处理最后一个步骤
  if (currentStep.trim()) {
    const trimmedStep = currentStep.trim();
    if (!seenSteps.has(trimmedStep)) {
      steps.push(trimmedStep);
      seenSteps.add(trimmedStep);
    }
  }

  // 如果没有分离出步骤，返回原始内容
  if (steps.length === 0) {
    return [cleanContent];
  }

  console.log('AgentThinking - 解析后的思考步骤:', steps);
  return steps.map(step => handleCitationFormat(step));
});

// 格式化引用: [citation:1] -> [1]
const handleCitationFormat = (content = '') => {
  return content.replace(/\[\s*citation:(\d+)\]/g, '[$1]');
};

const handleToggleThinking = () => {
  expanded.value = !expanded.value;
};
</script>

<style scoped lang="scss">
.agent-thinking-wrapper {
  margin-bottom: 16px;
}

.agent-thinking-box {
  width: fit-content;
  display: flex;
  border-radius: 12px;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 16px;
  cursor: pointer;
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(139, 126, 216, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(139, 126, 216, 0.15) 0%, rgba(183, 148, 246, 0.15) 100%);
    border-color: rgba(139, 126, 216, 0.5);
    transform: translateY(-1px);
  }

  .thinking-status {
    margin-right: 12px;
    padding: 0;
    color: var(--text-primary, #333);
    line-height: 1.4;
    font-weight: 500;
    font-size: 18px;
  }

  .thinking-toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary, #666);
    transition: transform 0.3s ease;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

.agent-thinking-content {
  color: var(--text-secondary, #666);
  font-size: 17px;
  border-left: 3px solid rgba(139, 126, 216, 0.3);
  padding-left: 16px;
  margin-top: 12px;
  margin-bottom: 16px;
  line-height: 1.6;
  background: rgba(139, 126, 216, 0.05);
  border-radius: 0 8px 8px 0;
  padding: 16px;

  .thinking-step {
    margin-bottom: 16px;
    padding: 12px 0;
    position: relative;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(139, 126, 216, 0.2);
      padding-bottom: 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &:before {
      content: '💭';
      position: absolute;
      left: -24px;
      top: 12px;
      font-size: 14px;
      opacity: 0.6;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .agent-thinking-box {
    padding: 10px 14px;
    font-size: 14px;
    border-radius: 10px;

    .thinking-status {
      margin-right: 8px;
    }

    .thinking-toggle-icon {
      svg {
        width: 14px;
        height: 14px;
      }
    }
  }

  .agent-thinking-content {
    font-size: 14px;
    padding: 12px;
    margin-top: 8px;
    margin-bottom: 12px;
    padding-left: 12px;

    .thinking-step {
      margin-bottom: 12px;
      padding: 8px 0;

      &:not(:last-child) {
        padding-bottom: 12px;
      }

      &:before {
        left: -20px;
        top: 8px;
        font-size: 12px;
      }
    }
  }
}
</style>

<template>
  <div class="agent-markdown-body custom-agent-markdown-body" @click="handleClick" v-html="mdText"></div>
  <QRCodePopup v-model:show="showQRCode" :url="qrCodeUrl" />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import MarkdownIt from 'markdown-it';
import markdownItLinkAttributes from 'markdown-it-link-attributes';
import markdownItFootnote from 'markdown-it-footnote';
import hljs from 'highlight.js/lib/core';
import javascript from 'highlight.js/lib/languages/javascript';
import 'highlight.js/styles/github.css';
import 'github-markdown-css/github-markdown-light.css';
import '@/styles/markdown.scss';
import QRCodePopup from '@/components/QRCode/QRCodePopup.vue';

hljs.registerLanguage('javascript', javascript);

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
});

// 创建 markdown-it 实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
  highlight: (str: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return renderCode(hljs.highlight(str, { language: lang, ignoreIllegals: true }).value);
      } catch (error) {
        console.warn('Highlight.js error:', error);
      }
    }
    return renderCode(str);
  },
});

// 链接属性配置接口
interface ILinkAttributesOptions {
  pattern: RegExp;
  attrs: {
    target: string;
    rel: string;
  };
}

md.use(markdownItFootnote);

md.use(markdownItLinkAttributes, {
  pattern: /^(https?|ftp):/,
  attrs: {
    target: '_blank',
    rel: 'noopener noreferrer',
  },
} as ILinkAttributesOptions);

// 定义代码渲染函数
function renderCode(originalCode: string): string {
  return `<pre class="hljs hljs-copy-wrapper"><code>${originalCode}</code></pre>`;
}

// 计算属性，将 markdown 文本转换为 HTML
const mdText = computed(() => md.render(props.text));

// 检测是否为移动设备
const isMobileDevice = () => {
  // 只通过User Agent判断真正的移动设备，不依赖窗口宽度
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// QR Code popup state
const showQRCode = ref(false);
const qrCodeUrl = ref('');

// 点击处理函数
const handleClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  // Handle link clicks
  if (target.tagName === 'A') {
    const url = target.getAttribute('href');
    if (url) {
      // 在移动设备上直接跳转链接
      if (isMobileDevice()) {
        // 不阻止默认行为，让链接正常打开
        return;
      }
      // 在桌面设备上显示二维码
      event.preventDefault();
      qrCodeUrl.value = url;
      showQRCode.value = true;
    }
  }
  // Handle question text clicks (if needed)
  if (target.classList.contains('q-text')) {
    // const text = target.innerText;
    // emit('sendQuestion', text);
  }
};
</script>

<style lang="scss" scoped>
.agent-markdown-body {
  // 基础样式
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 18px;
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;

  // 标题样式
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    color: var(--text-primary, #333);
  }

  :deep(h1) {
    font-size: 2em;
    border-bottom: 1px solid var(--border-light, #eaecef);
    padding-bottom: 0.3em;
  }

  :deep(h2) {
    font-size: 1.5em;
    border-bottom: 1px solid var(--border-light, #eaecef);
    padding-bottom: 0.3em;
  }

  :deep(h3) {
    font-size: 1.25em;
  }

  // 段落样式
  :deep(p) {
    margin-top: 0;
    margin-bottom: 16px;
  }

  // 列表样式
  :deep(ul), :deep(ol) {
    margin-top: 0;
    margin-bottom: 16px;
    padding-left: 2em;
  }

  :deep(li) {
    margin-bottom: 0.25em;
  }

  // 链接样式
  :deep(a) {
    color: var(--primary-color, #8B7ED8);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      color: var(--primary-color-dark, #7B6EC8);
      text-decoration: underline;
    }
  }

  // 代码样式
  :deep(code) {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(139, 126, 216, 0.1);
    border-radius: 6px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  }

  :deep(pre) {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 6px;
    margin-bottom: 16px;

    code {
      display: inline;
      max-width: auto;
      padding: 0;
      margin: 0;
      overflow: visible;
      line-height: inherit;
      word-wrap: normal;
      background-color: transparent;
      border: 0;
    }
  }

  // 引用样式
  :deep(blockquote) {
    padding: 0 1em;
    color: var(--text-secondary, #666);
    border-left: 0.25em solid var(--border-light, #dfe2e5);
    margin: 0 0 16px 0;

    > :first-child {
      margin-top: 0;
    }

    > :last-child {
      margin-bottom: 0;
    }
  }

  // 表格样式
  :deep(table) {
    border-spacing: 0;
    border-collapse: collapse;
    margin-bottom: 16px;
    width: 100%;
    overflow: auto;

    th, td {
      padding: 6px 13px;
      border: 1px solid var(--border-light, #dfe2e5);
    }

    th {
      font-weight: 600;
      background-color: var(--bg-light, #f6f8fa);
    }

    tr:nth-child(2n) {
      background-color: var(--bg-light, #f6f8fa);
    }
  }

  // 分割线样式
  :deep(hr) {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: var(--border-light, #e1e4e8);
    border: 0;
  }

  // 图片样式
  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .agent-markdown-body {
    font-size: 14px;

    :deep(h1) {
      font-size: 1.6em;
    }

    :deep(h2) {
      font-size: 1.3em;
    }

    :deep(h3) {
      font-size: 1.1em;
    }

    :deep(pre) {
      padding: 12px;
      font-size: 80%;
    }

    :deep(table) {
      font-size: 12px;

      th, td {
        padding: 4px 8px;
      }
    }
  }
}
</style>

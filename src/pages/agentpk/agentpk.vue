<template>
  <div class="agentpk-fullscreen">
    <!-- 顶部返回按钮 -->
    <div class="top-bar">
      <button class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 聊天内容区域 -->
      <div v-if="chatMessages.length > 0" class="chat-content">
        <div ref="scrollWrapper" class="chat-messages">
          <!-- 聊天消息列表 -->
          <template v-for="(item, index) in chatMessages" :key="item.key || index">
            <!-- 用户消息和AI回答使用AgentChatItem组件 -->
            <AgentChatItem
              v-if="item.role === 'user' || item.role === 'assistant'"
              :message-data="item"
            />

            <!-- 思考过程组件 -->
            <AgentThinkingProcess
              v-if="item.role === 'thinking'"
              :thinking-data="thinkingData"
            />
          </template>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <InputSection
          ref="inputSectionRef"
          @send-message="handleSendMessage"
          @batch-test="handleBatchTest"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted, ref, nextTick, onBeforeMount } from 'vue';
import { showFailToast } from 'vant';
import { Typewriter } from '@/utils/typeWriter';
import { comprehensiveSearchStream } from '@/apis/search';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import InputSection from './components/InputSection.vue';
import AgentChatItem from './components/AgentChatItem.vue';
import AgentThinkingProcess from './components/AgentThinkingProcess.vue';

const router = useRouter();

// 输入框引用
const inputSectionRef = ref<InstanceType<typeof InputSection> | null>(null);

// 滚动容器引用
const scrollWrapper = ref<HTMLElement | null>(null);

// 用户信息
const currentUserId = ref('');
const currentPersonId = ref('');

// 聊天相关状态
const chatMessages = ref<ExtendedChatMessage[]>([]);
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const isTypewriterStarted = ref(false);
const canSendMessage = ref(true);

// 定义扩展的消息类型，包含思考过程
type ExtendedChatMessage = IChatStreamContent | { role: 'thinking'; key: string | number };

// 思考过程数据
const thinkingData = ref<{
  items: Array<{
    type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
    message?: string;
    step?: 'start' | 'processing' | 'complete';
    question?: string;
    questions?: string[];
    results?: Array<{ title: string; link: string }>;
  }>;
  isLoading: boolean;
}>({
  items: [],
  isLoading: false,
});

// 创建打字机实例
const typewriter = new Typewriter(
  async (str: string) => {
    if (str) {
      console.log('🖨️ [agentpk] typewriter更新消息内容:', {
        displayLength: str.length,
        preview: str.substring(0, 50) + (str.length > 50 ? '...' : ''),
        messageIndex: chatMessages.value.length - 1,
        isStoppedByUser: isStoppedByUser.value,
      });

      // 添加安全检查，确保消息索引有效
      if (chatMessages.value.length > 0) {
        const lastMessage = chatMessages.value[chatMessages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = str;
          // 标记消息为完成状态
          lastMessage.isFinish = false; // 打字机过程中保持未完成状态
          await nextTick(() => {
            scrollToEnd();
          });
        } else {
          console.warn('⚠️ [agentpk] typewriter回调时最后一条消息不是助手消息');
        }
      } else {
        console.warn('⚠️ [agentpk] typewriter回调时消息列表为空');
      }
    }
  },
  () => {
    // 打字机自动完成回调
    console.log('✅ [agentpk] typewriter自动完成回调触发');
    isTypewriterStarted.value = false;
    // 标记最后一条助手消息为完成状态
    if (chatMessages.value.length > 0) {
      const lastMessage = chatMessages.value[chatMessages.value.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.isFinish = true;
      }
    }
    // 打字机完成时，允许发送新消息
    canSendMessage.value = true;
    streamController.value = null;
    // 重新启用输入框
    if (inputSectionRef.value) {
      inputSectionRef.value.setLoading(false);
    }
  },
);

// 滚动到底部
const scrollToEnd = () => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  }
};

// 处理发送消息
const handleSendMessage = async (message: string) => {
  console.log('🔄 [agentpk] 发送消息:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [agentpk] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  await sendSearchMessage(message);
};

// 发送搜索消息
const sendSearchMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [agentpk] 开始发送搜索消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [agentpk] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;
  canSendMessage.value = false;

  // 禁用输入框
  if (inputSectionRef.value) {
    inputSectionRef.value.setLoading(true);
  }

  // 清空之前的思考数据
  thinkingData.value.items = [];
  thinkingData.value.isLoading = false;

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: messageContent,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 添加思考过程占位符 - 使用特殊的消息类型
  const thinkingMessage = {
    role: 'thinking' as const,
    key: Date.now() + 1,
  };
  chatMessages.value.push(thinkingMessage);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  // 创建新的AbortController
  streamController.value = new AbortController();

  try {
    // 开始综合搜索
    await comprehensiveSearchStream(
      {
        question: messageContent,
        user_id: currentUserId.value,
      },
      {
        onStatus: (data) => {
          console.log('📊 [agentpk] 状态更新:', data);
          thinkingData.value.items.push({
            type: 'status',
            message: data.message,
            step: data.step,
          });
          if (data.step === 'start') {
            thinkingData.value.isLoading = true;
          } else if (data.step === 'complete') {
            thinkingData.value.isLoading = false;
          }
        },
        onQuestion: (data) => {
          console.log('❓ [agentpk] 问题:', data);
          thinkingData.value.items.push({
            type: 'question',
            question: data.question,
          });
        },
        onLog: (data) => {
          console.log('📝 [agentpk] 日志:', data);
          thinkingData.value.items.push({
            type: 'log',
            message: data.message,
          });
        },
        onKnowledgeContext: (data) => {
          console.log('📚 [agentpk] 知识上下文:', data);
          // 暂时不展示
        },
        onSearchQuestions: (data) => {
          console.log('🔍 [agentpk] 搜索问题:', data);
          thinkingData.value.items.push({
            type: 'search_questions',
            questions: data.questions,
          });
        },
        onSearchResult: (data) => {
          console.log('📋 [agentpk] 搜索结果:', data);
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          thinkingData.value.items.push({
            type: 'search_result',
            results,
          });
        },
        onFinalAnswer: (data) => {
          console.log('✅ [agentpk] 最终答案:', data);
          // 完成思考过程
          thinkingData.value.isLoading = false;

          // 构建完整答案
          const fullAnswer = `${data.core_answer}\n\n${data.detailed_answer}`;

          // 使用打字机显示答案
          typewriter.add(fullAnswer);
          if (!isTypewriterStarted.value) {
            console.log('🖨️ [agentpk] 启动typewriter，开始显示答案');
            isTypewriterStarted.value = true;
            typewriter.start();
          }
          typewriter.markFinished();
        },
        onError: (error) => {
          console.error('❌ [agentpk] 搜索错误:', error);
          canSendMessage.value = true;
          streamController.value = null;
          if (inputSectionRef.value) {
            inputSectionRef.value.setLoading(false);
          }
        },
        onClose: () => {
          console.log('🏁 [agentpk] 搜索连接关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [agentpk] 发送搜索消息失败:', error);
    canSendMessage.value = true;
    streamController.value = null;
    if (inputSectionRef.value) {
      inputSectionRef.value.setLoading(false);
    }
  }
};

// 处理批量测评
const handleBatchTest = () => {
  console.log('🔄 [agentpk] 批量测评');
  // 暂时在console打log，后续等其他AI接口来了后同时问三个AI
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [agentpk] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [agentpk] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [agentpk] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [agentpk] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [agentpk] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 返回首页
const goBack = async () => {
  console.log('🔙 [agentpk] 返回首页');
  await router.push({
    name: 'chat', // 返回到index.vue (chat路由)
  });
};

// 设置全屏样式
const setFullscreenStyles = () => {
  // 添加全屏样式类
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }

  console.log('🎨 [agentpk] 已应用全屏样式');
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }

  console.log('🎨 [agentpk] 已移除全屏样式');
};

// 页面挂载前的初始化
onBeforeMount(async () => {
  // 先加载用户信息
  await loadUserInfo();
});

// 页面挂载时的初始化
onMounted(async () => {
  console.log('🚀 [agentpk] 全屏页面加载完成');
  setFullscreenStyles();

  // 再次加载用户信息（确保用户信息已加载）
  await loadUserInfo();

  // 获取用户的完整 person_id
  if (currentUserId.value && currentUserId.value !== 'unknown_user') {
    try {
      const userProfile = await getUserProfile({
        user_id: currentUserId.value,
      });

      if (userProfile && userProfile.result === 'success' && userProfile.person) {
        currentPersonId.value = userProfile.person.person_id;
        console.log('👤 [agentpk] 用户 person_id 获取成功:', currentPersonId.value);
      } else {
        console.warn('⚠️ [agentpk] 获取用户 person_id 失败:', userProfile);
        currentPersonId.value = currentUserId.value; // 降级使用 user_id
      }
    } catch (profileError) {
      console.error('❌ [agentpk] 获取用户档案失败:', profileError);
      currentPersonId.value = currentUserId.value; // 降级使用 user_id
    }
  }

  // 初始化完成
  console.log('✅ [agentpk] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，恢复原始样式');
  removeFullscreenStyles();

  // 如果有正在进行的请求，取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();

  // 清理完成
  console.log('✅ [agentpk] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.agentpk-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: auto;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(139, 126, 216, 0.1);
  border: 1px solid rgba(139, 126, 216, 0.3);
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(139, 126, 216, 0.2);
    border-color: rgba(139, 126, 216, 0.5);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.main-content {
  position: absolute;
  top: 60px; // 顶部导航栏高度
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  // 当没有聊天内容时，输入框居中在40%位置
  &:not(:has(.chat-content)) {
    justify-content: flex-start;
    padding-top: calc(40vh - 60px); // 40%减去顶部导航栏高度

    .input-section {
      position: static;
      transform: none;
    }
  }

  .chat-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 20px 0;
      scroll-behavior: smooth;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(139, 126, 216, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(139, 126, 216, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: rgba(139, 126, 216, 0.5);
        }
      }
    }
  }

  .input-section {
    flex-shrink: 0;
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }
}



// 响应式设计
@media (max-width: 768px) {
  .top-bar {
    height: 50px;
    padding: 0 15px;
  }

  .back-button {
    padding: 6px 12px;
    font-size: 13px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .main-content {
    top: 50px; // 移动端顶部导航栏高度
    padding: 0 15px;

    // 移动端输入框40%位置调整
    &:not(:has(.chat-content)) {
      padding-top: calc(40vh - 50px); // 40%减去移动端顶部导航栏高度
    }

    .chat-content {
      .chat-messages {
        padding: 15px 0;
      }
    }

    .input-section {
      padding: 15px 0;
    }
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0 10px;

    .chat-content {
      .chat-messages {
        padding: 10px 0;
      }
    }

    .input-section {
      padding: 10px 0;
    }
  }
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}
</style>
